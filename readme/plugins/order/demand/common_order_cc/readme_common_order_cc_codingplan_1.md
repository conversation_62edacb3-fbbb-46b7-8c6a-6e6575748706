# 工单系统抄送功能 - 渐进式小步迭代式编码计划 迭代一

### **代码目录结构 (迭代一完成后)**

本次迭代将主要修改`order`插件，新增抄送相关的数据库操作和业务逻辑，并为后续迭代预留接口。

```
app/ops/bpm/plugins/
└── order/
    ├── order.go                      # [改造] Handler层：新增 ValidateUser 接口；改造 CommonOrder 接口编排抄送逻辑。
    ├── orderdto/
    │   └── biz_objects.go            # [改造] 新增 ValidatedUserInfo, CcUserInfo DTO，为列表查询预留参数对象。
    └── orderbiz/
        ├── order.go                  # [改造] Service层：新增用户校验、抄送记录创建的业务逻辑；为后续功能预留函数签名。
        └── internal/
            └── orderdao/
                ├── def_order_cc.go   # [新增] DAO层：封装对 tb_order_cc 表的原子 CRUD 操作。
                ├── ext_order.go      # [新增] DAO层：为迭代二、三预留复杂SQL查询函数（本次为空实现）。
                └── obj.go            # [改造] 新增 OrderCc 表结构体；为列表查询预留DAO参数。
└── orderproto/
    └── order.proto                 # [改造] Protobuf定义：新增 ValidateUser API；改造现有API以支持抄送和未来筛选功能。

```

### **受影响的现有模块说明**

1.  **`order.proto`**: 核心API定义文件。需要新增`ValidateUser`的`Request`和`Response`，并为`CommonOrderReq`添加抄送人列表字段。同时为迭代二、三提前在列表查询接口中加入`filter_by_role`和`role_type`占位字段。
2.  **`order.go` (Handler)**: `CommonOrder`方法是核心改造点，需在创建主工单后，增加调用“创建抄送记录”的逻辑。新增`ValidateUser`方法以暴露用户校验能力。
3.  **`orderbiz/order.go` (Service)**: 需新增`ValidateUserAndBuildInfo`和`CreateCcRecords`两个核心业务方法。同时，需改造`NewCreateStage`和`NewSendFeiShuAudit`的函数签名以传递抄送人信息，但内部逻辑暂时留白（占位）。列表查询函数也需改造签名，为后续筛选功能做准备。
4.  **`orderdao/obj.go`**: 需新增与`tb_order_cc`表映射的`OrderCc`结构体。
5.  **现有调用链**: `CommonOrder`的调用链中会插入创建抄送记录的环节。异步触发的`NewCreateStage` -> `NewSendFeiShuAudit`流程会增加参数透传，但实际行为不变。

### **开发流程Mermaid图**

下图清晰地展示了本次开发的迭代步骤和每个步骤的验证要点。

```mermaid
flowchart TD
    subgraph Step 1: 基础准备
        A[1.1: 创建数据库表 tb_order_cc] --> B[1.2: 定义各层数据结构];
        B --> C[1.3: 重新生成Protobuf代码];
    end
    subgraph " "
        direction LR
        A -- 可验证 --> V1[验证: 数据库表结构正确];
        C -- 可验证 --> V2[验证: 项目编译通过, 服务可正常启动];
    end

    subgraph Step 2: 实现DAO层
        D[2.1: 新增 def_order_cc.go 文件] --> E[2.2: 实现 BatchNewCcRecords 方法];
    end
    subgraph " "
        direction LR
        E -- 可验证 --> V3[验证: 可通过单元测试或临时代码调用, 成功写入数据];
    end
    
    subgraph Step 3: 实现用户校验API
        F["3.1 (Service): 实现 ValidateUserAndBuildInfo] --> G[3.2 (Handler): 实现 ValidateUser"];
    end
    subgraph " "
        direction LR
        G -- 可验证 --> V4[验证: 调用 /order/validate-user 接口, 能正确返回用户信息或错误提示];
    end
    
    subgraph Step 4: 集成核心抄送逻辑
        H["4.1 (Service): 实现 CreateCcRecords] --> I[4.2 (Handler): 改造 CommonOrder 方法"];
    end
    subgraph " "
        direction LR
        I -- 可验证 --> V5[验证: 创建带抄送人的工单, tb_order 和 tb_order_cc 表均写入正确数据];
    end

    subgraph Step 5: 改造异步流程与未来功能占位
        J[5.1: 改造异步函数签名以传递抄送人信息] --> K[5.2: 为列表查询功能添加占位符];
    end
    subgraph " "
        direction LR
        K -- 可验证 --> V6[验证: 创建工单功能正常, 列表查询API返回结构包含新占位字段且功能无回归性问题];
    end
    
    Step1 --> Step2 --> Step3 --> Step4 --> Step5;
```

---

### **渐进式小步迭代式开发与集成步骤**

以下是具体的编码步骤，请严格按照顺序执行。

#### **第一步：环境与结构准备 (Foundation)**

此步骤为所有后续开发奠定基础，不涉及复杂逻辑，完成后即可编译运行。

1.  **数据库操作**:
    *   **任务**: 根据设计文档，手动或使用迁移工具在数据库中创建`tb_order_cc`表。
    *   **验证**: 确认表结构、字段类型、主键及唯一索引与设计文档完全一致。

2.  **定义数据结构 (DAO & DTO & Proto)**:
    *   **任务**:
        *   在 `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/obj.go` 中，新增 `OrderCc` 结构体。
        *   在 `app/ops/bpm/plugins/order/orderdto/biz_objects.go` 中，新增 `ValidatedUserInfo` 和 `CcUserInfo` 结构体。
        *   在 `app/ops/bpm/plugins/orderproto/order.proto` 中，新增 `ValidateUserReq`, `ValidateUserResp`, `CcUserInfo` 消息体，并改造 `CommonOrderReq` 添加 `repeated CcUserInfo cc_user_infos` 字段。
    *   **验证**: 项目可以无错误地编译。

3.  **生成代码**:
    *   **任务**: 运行项目中的 `protoc` 相关命令，重新生成 `zz_generated.order.go` 等文件。
    *   **验证**: 确认服务可以成功启动，虽然新接口尚未实现，但服务框架已识别它们。

---

#### **第二步：实现抄送数据访问层 (DAO)**

此步骤聚焦于数据库操作的封装，使其可被业务层调用。

1.  **创建DAO文件**:
    *   **任务**: 在 `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/` 目录下，创建新文件 `def_order_cc.go`。

2.  **实现批量插入方法**:
    *   **任务**: 在 `def_order_cc.go` 中，实现 `BatchNewCcRecords(ctx context.Context, records []*obj.OrderCc) error` 方法。该方法应能将一个 `OrderCc` 对象切片高效地批量插入到 `tb_order_cc` 表中。
    *   **复用**: 参考现有DAO文件（如 `orderdao/order.go`）中的数据库操作方式，复用数据库连接和ORM工具。
    *   **验证**: 编写一个临时的单元测试，或在任何方便的地方临时调用此方法，验证其能够成功将数据写入数据库。

---

#### **第三步：实现并暴露用户校验API (End-to-End)**

此步骤完成一个完整、独立的新功能，是前端实现抄送人添加功能的前置依赖。

1.  **实现Service层逻辑**:
    *   **任务**: 在 `app/ops/bpm/plugins/order/orderbiz/order.go` 中，新增 `ValidateUserAndBuildInfo(ctx context.Context, email string) (*orderdto.ValidatedUserInfo, error)` 方法。
    *   **逻辑**: 调用现有的 `feishuservice`，通过邮箱获取用户信息和部门信息，然后组装成 `orderdto.ValidatedUserInfo` 返回。处理好用户不存在或查询失败的错误情况。
    *   **验证**: 可通过单元测试验证此方法的逻辑正确性。

2.  **实现Handler层接口**:
    *   **任务**: 在 `app/ops/bpm/plugins/order/order.go` 中，实现 `ValidateUser(ctx context.Context, req *orderproto.ValidateUserReq) (*orderproto.ValidateUserResp, error)` 方法。
    *   **逻辑**: 调用上一步创建的 `ValidateUserAndBuildInfo` Service方法，并将结果转换为`proto`响应格式。
    *   **验证**: 启动服务，使用 `curl` 或 Postman 等工具向 `/order/validate-user` 接口发送请求，测试以下两种情况：
        1.  使用存在的内部员工邮箱，应返回 `ret: 0` 及用户信息。
        2.  使用不存在的邮箱，应返回对应的业务错误码和提示信息。

---

#### **第四步：在创建工单流程中集成抄送功能**

此步骤将抄送功能的核心逻辑与现有工单创建流程结合。

1.  **实现Service层逻辑**:
    *   **任务**: 在 `app/ops/bpm/plugins/order/orderbiz/order.go` 中，新增 `CreateCcRecords(ctx context.Context, orderID string, ccUserInfos []*orderdto.CcUserInfo) error` 方法。
    *   **逻辑**:
        1.  将 `orderdto.CcUserInfo` 切片转换为 `orderdao.OrderCc` 切片。
        2.  调用第二步中创建的 `orderdao.BatchNewCcRecords` 方法将数据写入数据库。
    *   **验证**: 可通过单元测试验证此方法的逻辑正确性。

2.  **改造Handler层 `CommonOrder` 方法**:
    *   **任务**: 修改 `app/ops/bpm/plugins/order/order.go` 中的 `CommonOrder` 方法。
    *   **逻辑**:
        1.  在调用 `s.biz.NewCreateOrder` 成功创建主工单后。
        2.  检查请求体 `req.CcUserInfos` 是否存在且不为空。
        3.  如果存在，则将 `proto.CcUserInfo` 转换为 `dto.CcUserInfo`，然后调用上一步创建的 `s.biz.CreateCcRecords` 方法。
        4.  根据设计要求，记录该过程中的任何错误日志，但不应中断主流程或导致API返回失败。
    *   **验证**: 启动服务，调用 `/order/common` 接口创建一个带有 `cc_user_infos` 字段的工单。检查数据库，应能看到 `tb_order` 和 `tb_order_cc` 中都成功写入了相应的数据。

---

#### **第五步：为未来迭代添加占位符**

此步骤确保了API和代码结构的前瞻性，为后续迭代铺平道路，避免未来进行破坏性修改。

1.  **改造异步流程函数签名**:
    *   **任务**:
        *   修改 `order.go` (Handler) 中 `go s.biz.NewCreateStage(...)` 的调用，将 `req.CcUserInfos` (转换后的DTO) 传递进去。
        *   修改 `orderbiz/order.go` (Service) 中 `NewCreateStage` 和 `NewSendFeiShuAudit` 的函数签名，以接收 `ccUserInfos` 参数。
        *   在 `NewSendFeiShuAudit` 内部，暂时无需实现抄送通知逻辑，只需添加 `// TODO: 迭代三实现抄送通知` 注释即可。
    *   **验证**: 重复第四步的验证，确保添加参数后，创建工单的整体流程依然正常，没有出现编译错误或运行时恐慌。

2.  **改造列表查询API结构**:
    *   **任务**:
        *   在 `order.proto` 中，为 `GetMyDoingOrderReq`, `GetMyDoneOrderReq`, `GetMyAuditOrderReq` 添加 `repeated string filter_by_role = 10;`。
        *   在 `order.proto` 的 `OrderListItem` 中，添加 `string role_type = 20;`。
        *   重新生成`proto`代码。
        *   在 `orderbiz/order.go` (Service) 的相关列表查询函数（如`GetDoingOrdersByPage`）中，修改返回的 `Order` 对象，为其 `RoleType` 字段赋一个默认空字符串 `""`。暂时忽略请求中的 `filter_by_role` 参数。
        *   在 `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/` 下创建 `ext_order.go` 文件，并添加一个空的占位函数 `GetMyOrdersWithRole`，直接返回 `nil, nil`。
    *   **验证**: 启动服务，调用任一工单列表查询接口（如 `/GetMyDoingOrder`）。接口应能正常返回数据，且每条记录中都包含一个空的 `role_type` 字段。这证明了接口的向后兼容性，且为前端并行开发做好了准备。

完成以上所有步骤后，迭代一的后端开发任务便宣告完成。代码库将处于一个功能完整、结构清晰且易于扩展的状态。