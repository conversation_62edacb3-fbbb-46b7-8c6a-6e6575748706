### 节点: (dao *OrderMysqlDao) GetDoneOrdersCountByProposerEmail
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/def_order.go` (接口定义), `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/zz_generated.def_order.go` (自动生成的实现)
- **用途**: 统计指定用户已完结的工单总数。
- **输入参数**:
    - `proposerEmail string`: 申请人的邮箱地址。
- **输出说明**:
    - `*SelectCount`: 包含总数的结果。
    - `error`: 如果数据库查询失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B[执行SELECT COUNT(*) SQL语句];
    B --> C[返回结果];
    C --> D[结束];
```

### 节点: (dao *OrderMysqlDao) GetDoneOrdersByProposerEmail
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/def_order.go` (接口定义), `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/zz_generated.def_order.go` (自动生成的实现)
- **用途**: 查询指定用户已完结的工单列表（分页）。
- **输入参数**:
    - `proposerEmail string`: 申请人的邮箱地址。
    - `offset int64`: 分页查询的起始位置。
    - `rows int64`: 查询的记录数。
- **输出说明**:
    - `[]TbOrder`: 工单列表。
    - `error`: 如果数据库查询失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B[执行SELECT SQL语句];
    B --> C[返回结果];
    C --> D[结束];
```